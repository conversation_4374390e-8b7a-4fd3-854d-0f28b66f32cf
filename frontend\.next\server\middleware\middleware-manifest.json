{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_adbadb87._.js", "server/edge/chunks/[root-of-the-server]__e07e566a._.js", "server/edge/chunks/edge-wrapper_9cb4b359.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "4L1N1ApV6JxRkywf2zSy/shy6TmjdExKn7foFPP8JTY=", "__NEXT_PREVIEW_MODE_ID": "8fad4a49bbf54c564d8550f313a74e06", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "03fbffbf4bcdd731e7ef93088f69be7a4c14ca77a318dac082d5ec0219a1f37a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "072e993b944073a503a8edeceb372cd7622c438dc634f5ee43747c99c6c07f95"}}}, "instrumentation": null, "functions": {}}